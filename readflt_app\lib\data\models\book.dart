import 'package:uuid/uuid.dart';

class Book {
  final String id;
  final String title;
  final String author;
  final String? description;
  final String filePath;
  final String format;
  final String? coverPath;
  final int totalPages;
  final int currentPage;
  final double progress;
  final DateTime addedDate;
  final DateTime? lastReadDate;
  final bool isFavorite;
  final int fileSize;
  final String? isbn;
  final String? publisher;
  final DateTime? publishDate;
  final List<String> tags;

  Book({
    String? id,
    required this.title,
    required this.author,
    this.description,
    required this.filePath,
    required this.format,
    this.coverPath,
    this.totalPages = 0,
    this.currentPage = 0,
    this.progress = 0.0,
    DateTime? addedDate,
    this.lastReadDate,
    this.isFavorite = false,
    this.fileSize = 0,
    this.isbn,
    this.publisher,
    this.publishDate,
    this.tags = const [],
  }) : id = id ?? const Uuid().v4(),
       addedDate = addedDate ?? DateTime.now();

  Book copyWith({
    String? id,
    String? title,
    String? author,
    String? description,
    String? filePath,
    String? format,
    String? coverPath,
    int? totalPages,
    int? currentPage,
    double? progress,
    DateTime? addedDate,
    DateTime? lastReadDate,
    bool? isFavorite,
    int? fileSize,
    String? isbn,
    String? publisher,
    DateTime? publishDate,
    List<String>? tags,
  }) {
    return Book(
      id: id ?? this.id,
      title: title ?? this.title,
      author: author ?? this.author,
      description: description ?? this.description,
      filePath: filePath ?? this.filePath,
      format: format ?? this.format,
      coverPath: coverPath ?? this.coverPath,
      totalPages: totalPages ?? this.totalPages,
      currentPage: currentPage ?? this.currentPage,
      progress: progress ?? this.progress,
      addedDate: addedDate ?? this.addedDate,
      lastReadDate: lastReadDate ?? this.lastReadDate,
      isFavorite: isFavorite ?? this.isFavorite,
      fileSize: fileSize ?? this.fileSize,
      isbn: isbn ?? this.isbn,
      publisher: publisher ?? this.publisher,
      publishDate: publishDate ?? this.publishDate,
      tags: tags ?? this.tags,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'author': author,
      'description': description,
      'filePath': filePath,
      'format': format,
      'coverPath': coverPath,
      'totalPages': totalPages,
      'currentPage': currentPage,
      'progress': progress,
      'addedDate': addedDate.millisecondsSinceEpoch,
      'lastReadDate': lastReadDate?.millisecondsSinceEpoch,
      'isFavorite': isFavorite ? 1 : 0,
      'fileSize': fileSize,
      'isbn': isbn,
      'publisher': publisher,
      'publishDate': publishDate?.millisecondsSinceEpoch,
      'tags': tags.join(','),
    };
  }

  factory Book.fromMap(Map<String, dynamic> map) {
    return Book(
      id: map['id'],
      title: map['title'],
      author: map['author'],
      description: map['description'],
      filePath: map['filePath'],
      format: map['format'],
      coverPath: map['coverPath'],
      totalPages: map['totalPages'] ?? 0,
      currentPage: map['currentPage'] ?? 0,
      progress: map['progress'] ?? 0.0,
      addedDate: DateTime.fromMillisecondsSinceEpoch(map['addedDate']),
      lastReadDate: map['lastReadDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['lastReadDate'])
          : null,
      isFavorite: map['isFavorite'] == 1,
      fileSize: map['fileSize'] ?? 0,
      isbn: map['isbn'],
      publisher: map['publisher'],
      publishDate: map['publishDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['publishDate'])
          : null,
      tags: map['tags'] != null && map['tags'].isNotEmpty 
          ? map['tags'].split(',') 
          : [],
    );
  }

  @override
  String toString() {
    return 'Book{id: $id, title: $title, author: $author, format: $format}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Book && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
