import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/book.dart';
import '../models/collection.dart';
import '../models/reading_settings.dart';
import '../../core/constants/app_constants.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConstants.databaseName);

    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Books table
    await db.execute('''
      CREATE TABLE books (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        author TEXT NOT NULL,
        description TEXT,
        filePath TEXT NOT NULL,
        format TEXT NOT NULL,
        coverPath TEXT,
        totalPages INTEGER DEFAULT 0,
        currentPage INTEGER DEFAULT 0,
        progress REAL DEFAULT 0.0,
        addedDate INTEGER NOT NULL,
        lastReadDate INTEGER,
        isFavorite INTEGER DEFAULT 0,
        fileSize INTEGER DEFAULT 0,
        isbn TEXT,
        publisher TEXT,
        publishDate INTEGER,
        tags TEXT
      )
    ''');

    // Collections table
    await db.execute('''
      CREATE TABLE collections (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        coverPath TEXT,
        createdDate INTEGER NOT NULL,
        modifiedDate INTEGER,
        bookCount INTEGER DEFAULT 0,
        isDefault INTEGER DEFAULT 0
      )
    ''');

    // Book-Collection relationship table
    await db.execute('''
      CREATE TABLE book_collections (
        id TEXT PRIMARY KEY,
        bookId TEXT NOT NULL,
        collectionId TEXT NOT NULL,
        addedDate INTEGER NOT NULL,
        FOREIGN KEY (bookId) REFERENCES books (id) ON DELETE CASCADE,
        FOREIGN KEY (collectionId) REFERENCES collections (id) ON DELETE CASCADE,
        UNIQUE(bookId, collectionId)
      )
    ''');

    // Reading settings table
    await db.execute('''
      CREATE TABLE reading_settings (
        id INTEGER PRIMARY KEY,
        fontSize REAL DEFAULT 16.0,
        fontFamily TEXT DEFAULT 'System',
        lineHeight REAL DEFAULT 1.5,
        letterSpacing REAL DEFAULT 0.0,
        backgroundColor INTEGER DEFAULT 4294967295,
        textColor INTEGER DEFAULT 4278190080,
        isDarkMode INTEGER DEFAULT 0,
        brightness REAL DEFAULT 1.0,
        pageTransition INTEGER DEFAULT 0,
        keepScreenOn INTEGER DEFAULT 0,
        showPageNumbers INTEGER DEFAULT 1,
        showProgress INTEGER DEFAULT 1,
        marginHorizontal REAL DEFAULT 16.0,
        marginVertical REAL DEFAULT 24.0
      )
    ''');

    // Create default collections
    await _createDefaultCollections(db);
    
    // Insert default reading settings
    await _insertDefaultReadingSettings(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < newVersion) {
      // Add migration logic here when needed
    }
  }

  Future<void> _createDefaultCollections(Database db) async {
    final defaultCollections = [
      Collection(
        name: '我的收藏',
        description: '默认收藏夹',
        isDefault: true,
      ),
      Collection(
        name: '最近阅读',
        description: '最近阅读的书籍',
        isDefault: true,
      ),
    ];

    for (final collection in defaultCollections) {
      await db.insert('collections', collection.toMap());
    }
  }

  Future<void> _insertDefaultReadingSettings(Database db) async {
    const defaultSettings = ReadingSettings();
    await db.insert('reading_settings', defaultSettings.toMap());
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  Future<void> deleteDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConstants.databaseName);
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
