import 'package:sqflite/sqflite.dart';
import '../models/book.dart';
import 'database_helper.dart';

class BookDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  Future<String> insertBook(Book book) async {
    final db = await _databaseHelper.database;
    await db.insert('books', book.toMap());
    return book.id;
  }

  Future<Book?> getBookById(String id) async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'books',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Book.fromMap(maps.first);
    }
    return null;
  }

  Future<List<Book>> getAllBooks() async {
    final db = await _databaseHelper.database;
    final maps = await db.query('books', orderBy: 'addedDate DESC');
    return maps.map((map) => Book.fromMap(map)).toList();
  }

  Future<List<Book>> getBooksByFormat(String format) async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'books',
      where: 'format = ?',
      whereArgs: [format],
      orderBy: 'addedDate DESC',
    );
    return maps.map((map) => Book.fromMap(map)).toList();
  }

  Future<List<Book>> getFavoriteBooks() async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'books',
      where: 'isFavorite = ?',
      whereArgs: [1],
      orderBy: 'lastReadDate DESC',
    );
    return maps.map((map) => Book.fromMap(map)).toList();
  }

  Future<List<Book>> getRecentlyReadBooks({int limit = 10}) async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'books',
      where: 'lastReadDate IS NOT NULL',
      orderBy: 'lastReadDate DESC',
      limit: limit,
    );
    return maps.map((map) => Book.fromMap(map)).toList();
  }

  Future<List<Book>> searchBooks(String query) async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'books',
      where: 'title LIKE ? OR author LIKE ? OR description LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'addedDate DESC',
    );
    return maps.map((map) => Book.fromMap(map)).toList();
  }

  Future<List<Book>> getBooksByTag(String tag) async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'books',
      where: 'tags LIKE ?',
      whereArgs: ['%$tag%'],
      orderBy: 'addedDate DESC',
    );
    return maps.map((map) => Book.fromMap(map)).toList();
  }

  Future<int> updateBook(Book book) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'books',
      book.toMap(),
      where: 'id = ?',
      whereArgs: [book.id],
    );
  }

  Future<int> updateBookProgress(String bookId, int currentPage, double progress) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'books',
      {
        'currentPage': currentPage,
        'progress': progress,
        'lastReadDate': DateTime.now().millisecondsSinceEpoch,
      },
      where: 'id = ?',
      whereArgs: [bookId],
    );
  }

  Future<int> toggleBookFavorite(String bookId) async {
    final db = await _databaseHelper.database;
    final book = await getBookById(bookId);
    if (book != null) {
      return await db.update(
        'books',
        {'isFavorite': book.isFavorite ? 0 : 1},
        where: 'id = ?',
        whereArgs: [bookId],
      );
    }
    return 0;
  }

  Future<int> deleteBook(String id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'books',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<int> getBookCount() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM books');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  Future<Map<String, int>> getBookCountByFormat() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT format, COUNT(*) as count FROM books GROUP BY format'
    );
    
    final Map<String, int> counts = {};
    for (final row in result) {
      counts[row['format'] as String] = row['count'] as int;
    }
    return counts;
  }

  Future<List<String>> getAllTags() async {
    final db = await _databaseHelper.database;
    final maps = await db.query('books', columns: ['tags']);

    final Set<String> allTags = {};
    for (final map in maps) {
      final tags = map['tags'] as String?;
      if (tags != null && tags.isNotEmpty) {
        allTags.addAll(tags.split(',').map((tag) => tag.trim()));
      }
    }

    return allTags.where((tag) => tag.isNotEmpty).toList()..sort();
  }
}
