import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/collection.dart';
import '../../data/models/book.dart';
import '../../data/database/collection_dao.dart';
import '../../data/database/book_dao.dart';

class CollectionState {
  final List<Collection> collections;
  final List<Collection> userCollections;
  final List<Collection> defaultCollections;
  final bool isLoading;
  final String? error;
  final Collection? selectedCollection;
  final List<Book> booksInSelectedCollection;

  const CollectionState({
    this.collections = const [],
    this.userCollections = const [],
    this.defaultCollections = const [],
    this.isLoading = false,
    this.error,
    this.selectedCollection,
    this.booksInSelectedCollection = const [],
  });

  CollectionState copyWith({
    List<Collection>? collections,
    List<Collection>? userCollections,
    List<Collection>? defaultCollections,
    bool? isLoading,
    String? error,
    Collection? selectedCollection,
    List<Book>? booksInSelectedCollection,
  }) {
    return CollectionState(
      collections: collections ?? this.collections,
      userCollections: userCollections ?? this.userCollections,
      defaultCollections: defaultCollections ?? this.defaultCollections,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      selectedCollection: selectedCollection ?? this.selectedCollection,
      booksInSelectedCollection: booksInSelectedCollection ?? this.booksInSelectedCollection,
    );
  }
}

class CollectionNotifier extends StateNotifier<CollectionState> {
  final CollectionDao _collectionDao;
  final BookDao _bookDao;

  CollectionNotifier(this._collectionDao, this._bookDao) : super(const CollectionState()) {
    loadCollections();
  }

  Future<void> loadCollections() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final allCollections = await _collectionDao.getAllCollections();
      final userCollections = await _collectionDao.getUserCollections();
      final defaultCollections = await _collectionDao.getDefaultCollections();
      
      state = state.copyWith(
        collections: allCollections,
        userCollections: userCollections,
        defaultCollections: defaultCollections,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> createCollection(String name, {String? description}) async {
    try {
      final collection = Collection(
        name: name,
        description: description,
      );
      
      await _collectionDao.insertCollection(collection);
      await loadCollections();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> updateCollection(Collection collection) async {
    try {
      await _collectionDao.updateCollection(collection);
      await loadCollections();
      
      // Update selected collection if it was the one being updated
      if (state.selectedCollection?.id == collection.id) {
        state = state.copyWith(selectedCollection: collection);
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> deleteCollection(String collectionId) async {
    try {
      await _collectionDao.deleteCollection(collectionId);
      await loadCollections();
      
      // Clear selected collection if it was deleted
      if (state.selectedCollection?.id == collectionId) {
        state = state.copyWith(selectedCollection: null, booksInSelectedCollection: []);
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> selectCollection(Collection collection) async {
    state = state.copyWith(selectedCollection: collection, isLoading: true);
    
    try {
      final books = await _collectionDao.getBooksInCollection(collection.id);
      state = state.copyWith(
        booksInSelectedCollection: books,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> addBookToCollection(String bookId, String collectionId) async {
    try {
      await _collectionDao.addBookToCollection(bookId, collectionId);
      
      // Reload collections to update book counts
      await loadCollections();
      
      // Reload books in selected collection if it's the one we added to
      if (state.selectedCollection?.id == collectionId) {
        await selectCollection(state.selectedCollection!);
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> removeBookFromCollection(String bookId, String collectionId) async {
    try {
      await _collectionDao.removeBookFromCollection(bookId, collectionId);
      
      // Reload collections to update book counts
      await loadCollections();
      
      // Reload books in selected collection if it's the one we removed from
      if (state.selectedCollection?.id == collectionId) {
        await selectCollection(state.selectedCollection!);
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<List<Collection>> getCollectionsForBook(String bookId) async {
    try {
      return await _collectionDao.getCollectionsForBook(bookId);
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return [];
    }
  }

  Future<bool> isBookInCollection(String bookId, String collectionId) async {
    try {
      return await _collectionDao.isBookInCollection(bookId, collectionId);
    } catch (e) {
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  void clearSelection() {
    state = state.copyWith(
      selectedCollection: null,
      booksInSelectedCollection: [],
    );
  }
}

// Providers
final collectionDaoProvider = Provider<CollectionDao>((ref) => CollectionDao());

final collectionProvider = StateNotifierProvider<CollectionNotifier, CollectionState>((ref) {
  final collectionDao = ref.watch(collectionDaoProvider);
  final bookDao = ref.watch(bookDaoProvider);
  return CollectionNotifier(collectionDao, bookDao);
});

// Helper providers
final favoriteCollectionProvider = FutureProvider<Collection?>((ref) async {
  final collectionDao = ref.watch(collectionDaoProvider);
  return await collectionDao.getFavoriteCollection();
});

final recentCollectionProvider = FutureProvider<Collection?>((ref) async {
  final collectionDao = ref.watch(collectionDaoProvider);
  return await collectionDao.getRecentCollection();
});

final collectionStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final collectionDao = ref.watch(collectionDaoProvider);
  final totalCollections = await collectionDao.getCollectionCount();
  final userCollections = await collectionDao.getUserCollections();
  
  return {
    'totalCollections': totalCollections,
    'userCollections': userCollections.length,
  };
});
