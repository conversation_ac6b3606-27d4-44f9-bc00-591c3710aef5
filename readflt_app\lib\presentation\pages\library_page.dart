import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../providers/library_provider.dart';
import '../widgets/book_card.dart';
import '../widgets/book_list_item.dart';
import '../../core/constants/app_constants.dart';

class LibraryPage extends ConsumerStatefulWidget {
  const LibraryPage({super.key});

  @override
  ConsumerState<LibraryPage> createState() => _LibraryPageState();
}

class _LibraryPageState extends ConsumerState<LibraryPage> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      ref.read(libraryProvider.notifier).setSearchQuery(_searchController.text);
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final libraryState = ref.watch(libraryProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('书库'),
        actions: [
          IconButton(
            icon: Icon(
              libraryState.viewMode == ViewMode.grid
                  ? Icons.view_list
                  : Icons.grid_view,
            ),
            onPressed: () {
              ref.read(libraryProvider.notifier).setViewMode(
                libraryState.viewMode == ViewMode.grid
                    ? ViewMode.list
                    : ViewMode.grid,
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
          ),
          PopupMenuButton<SortOption>(
            icon: const Icon(Icons.sort),
            onSelected: (option) {
              ref.read(libraryProvider.notifier).setSortOption(option);
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: SortOption.titleAsc,
                child: Text('标题 A-Z'),
              ),
              const PopupMenuItem(
                value: SortOption.titleDesc,
                child: Text('标题 Z-A'),
              ),
              const PopupMenuItem(
                value: SortOption.authorAsc,
                child: Text('作者 A-Z'),
              ),
              const PopupMenuItem(
                value: SortOption.authorDesc,
                child: Text('作者 Z-A'),
              ),
              const PopupMenuItem(
                value: SortOption.dateAddedDesc,
                child: Text('最新添加'),
              ),
              const PopupMenuItem(
                value: SortOption.dateAddedAsc,
                child: Text('最早添加'),
              ),
              const PopupMenuItem(
                value: SortOption.lastReadDesc,
                child: Text('最近阅读'),
              ),
              const PopupMenuItem(
                value: SortOption.progressDesc,
                child: Text('阅读进度'),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          _buildFilterChips(),
          Expanded(child: _buildBookList()),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showImportDialog(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: '搜索书籍...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChips() {
    final libraryState = ref.watch(libraryProvider);
    
    if (libraryState.selectedFormat == null &&
        libraryState.selectedTags.isEmpty &&
        !libraryState.showFavoritesOnly) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          if (libraryState.showFavoritesOnly)
            Padding(
              padding: const EdgeInsets.only(right: 8),
              child: FilterChip(
                label: const Text('收藏'),
                selected: true,
                onSelected: (_) {
                  ref.read(libraryProvider.notifier).toggleFavoritesOnly();
                },
              ),
            ),
          if (libraryState.selectedFormat != null)
            Padding(
              padding: const EdgeInsets.only(right: 8),
              child: FilterChip(
                label: Text(libraryState.selectedFormat!.toUpperCase()),
                selected: true,
                onSelected: (_) {
                  ref.read(libraryProvider.notifier).setFormatFilter(null);
                },
              ),
            ),
          ...libraryState.selectedTags.map(
            (tag) => Padding(
              padding: const EdgeInsets.only(right: 8),
              child: FilterChip(
                label: Text(tag),
                selected: true,
                onSelected: (_) {
                  ref.read(libraryProvider.notifier).toggleTagFilter(tag);
                },
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              ref.read(libraryProvider.notifier).clearAllFilters();
            },
            child: const Text('清除筛选'),
          ),
        ],
      ),
    );
  }

  Widget _buildBookList() {
    final libraryState = ref.watch(libraryProvider);

    if (libraryState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (libraryState.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              libraryState.error!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.read(libraryProvider.notifier).loadBooks();
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (libraryState.filteredBooks.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.library_books_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              libraryState.books.isEmpty ? '书库为空' : '没有找到匹配的书籍',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              libraryState.books.isEmpty
                  ? '点击右下角的 + 按钮添加书籍'
                  : '尝试调整搜索条件或筛选器',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (libraryState.viewMode == ViewMode.grid) {
      return _buildGridView(libraryState);
    } else {
      return _buildListView(libraryState);
    }
  }

  Widget _buildGridView(LibraryState libraryState) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: MasonryGridView.count(
        crossAxisCount: 2,
        mainAxisSpacing: AppConstants.defaultPadding,
        crossAxisSpacing: AppConstants.defaultPadding,
        itemCount: libraryState.filteredBooks.length,
        itemBuilder: (context, index) {
          final book = libraryState.filteredBooks[index];
          return BookCard(
            book: book,
            onTap: () => _openBook(book),
            onFavoriteToggle: () {
              ref.read(libraryProvider.notifier).toggleBookFavorite(book.id);
            },
            onDelete: () {
              ref.read(libraryProvider.notifier).deleteBook(book.id);
            },
          );
        },
      ),
    );
  }

  Widget _buildListView(LibraryState libraryState) {
    return ListView.builder(
      itemCount: libraryState.filteredBooks.length,
      itemBuilder: (context, index) {
        final book = libraryState.filteredBooks[index];
        return BookListItem(
          book: book,
          onTap: () => _openBook(book),
          onFavoriteToggle: () {
            ref.read(libraryProvider.notifier).toggleBookFavorite(book.id);
          },
          onDelete: () {
            ref.read(libraryProvider.notifier).deleteBook(book.id);
          },
        );
      },
    );
  }

  void _openBook(book) {
    // TODO: Navigate to reading page
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('打开书籍: ${book.title}')),
    );
  }

  void _showFilterDialog(BuildContext context) {
    // TODO: Implement filter dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('筛选功能开发中...')),
    );
  }

  void _showImportDialog(BuildContext context) {
    // TODO: Implement import dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('导入功能开发中...')),
    );
  }
}
