import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../providers/collection_provider.dart';
import '../widgets/collection_card.dart';
import '../widgets/book_card.dart';
import '../../core/constants/app_constants.dart';

class CollectionsPage extends ConsumerStatefulWidget {
  const CollectionsPage({super.key});

  @override
  ConsumerState<CollectionsPage> createState() => _CollectionsPageState();
}

class _CollectionsPageState extends ConsumerState<CollectionsPage> {
  @override
  Widget build(BuildContext context) {
    final collectionState = ref.watch(collectionProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('收藏夹'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCreateCollectionDialog(context),
          ),
        ],
      ),
      body: collectionState.selectedCollection == null
          ? _buildCollectionsList(collectionState)
          : _buildCollectionDetail(collectionState),
    );
  }

  Widget _buildCollectionsList(CollectionState collectionState) {
    if (collectionState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (collectionState.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              collectionState.error!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.read(collectionProvider.notifier).loadCollections();
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (collectionState.collections.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.collections_bookmark_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              '暂无收藏夹',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            const Text('点击右上角的 + 按钮创建收藏夹'),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (collectionState.defaultCollections.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Text(
              '系统收藏夹',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
              itemCount: collectionState.defaultCollections.length,
              itemBuilder: (context, index) {
                final collection = collectionState.defaultCollections[index];
                return SizedBox(
                  width: 150,
                  child: Padding(
                    padding: const EdgeInsets.only(right: AppConstants.smallPadding),
                    child: CollectionCard(
                      collection: collection,
                      onTap: () => _selectCollection(collection),
                      showActions: false,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
        if (collectionState.userCollections.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Text(
              '我的收藏夹',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
              child: MasonryGridView.count(
                crossAxisCount: 2,
                mainAxisSpacing: AppConstants.defaultPadding,
                crossAxisSpacing: AppConstants.defaultPadding,
                itemCount: collectionState.userCollections.length,
                itemBuilder: (context, index) {
                  final collection = collectionState.userCollections[index];
                  return CollectionCard(
                    collection: collection,
                    onTap: () => _selectCollection(collection),
                    onEdit: () => _showEditCollectionDialog(context, collection),
                    onDelete: () => _deleteCollection(collection.id),
                  );
                },
              ),
            ),
          ),
        ] else if (collectionState.defaultCollections.isNotEmpty) ...[
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.collections_bookmark_outlined,
                    size: 64,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '暂无自定义收藏夹',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  const Text('点击右上角的 + 按钮创建收藏夹'),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCollectionDetail(CollectionState collectionState) {
    final collection = collectionState.selectedCollection!;
    
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
          ),
          child: Row(
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  ref.read(collectionProvider.notifier).clearSelection();
                },
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      collection.name,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (collection.description != null)
                      Text(
                        collection.description!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    Text(
                      '${collection.bookCount} 本书',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              if (!collection.isDefault) ...[
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () => _showEditCollectionDialog(context, collection),
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: () => _deleteCollection(collection.id),
                ),
              ],
            ],
          ),
        ),
        Expanded(
          child: _buildBooksInCollection(collectionState),
        ),
      ],
    );
  }

  Widget _buildBooksInCollection(CollectionState collectionState) {
    if (collectionState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (collectionState.booksInSelectedCollection.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.library_books_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              '收藏夹为空',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            const Text('从书库中添加书籍到此收藏夹'),
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: MasonryGridView.count(
        crossAxisCount: 2,
        mainAxisSpacing: AppConstants.defaultPadding,
        crossAxisSpacing: AppConstants.defaultPadding,
        itemCount: collectionState.booksInSelectedCollection.length,
        itemBuilder: (context, index) {
          final book = collectionState.booksInSelectedCollection[index];
          return BookCard(
            book: book,
            onTap: () => _openBook(book),
            onFavoriteToggle: () {
              // TODO: Implement favorite toggle
            },
            onDelete: () => _removeBookFromCollection(book.id),
          );
        },
      ),
    );
  }

  void _selectCollection(collection) {
    ref.read(collectionProvider.notifier).selectCollection(collection);
  }

  void _deleteCollection(String collectionId) {
    ref.read(collectionProvider.notifier).deleteCollection(collectionId);
  }

  void _removeBookFromCollection(String bookId) {
    final collection = ref.read(collectionProvider).selectedCollection;
    if (collection != null) {
      ref.read(collectionProvider.notifier).removeBookFromCollection(bookId, collection.id);
    }
  }

  void _openBook(book) {
    // TODO: Navigate to reading page
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('打开书籍: ${book.title}')),
    );
  }

  void _showCreateCollectionDialog(BuildContext context) {
    // TODO: Implement create collection dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('创建收藏夹功能开发中...')),
    );
  }

  void _showEditCollectionDialog(BuildContext context, collection) {
    // TODO: Implement edit collection dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('编辑收藏夹功能开发中...')),
    );
  }
}
