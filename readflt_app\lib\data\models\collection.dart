import 'package:uuid/uuid.dart';

class Collection {
  final String id;
  final String name;
  final String? description;
  final String? coverPath;
  final DateTime createdDate;
  final DateTime? modifiedDate;
  final int bookCount;
  final bool isDefault;

  Collection({
    String? id,
    required this.name,
    this.description,
    this.coverPath,
    DateTime? createdDate,
    this.modifiedDate,
    this.bookCount = 0,
    this.isDefault = false,
  }) : id = id ?? const Uuid().v4(),
       createdDate = createdDate ?? DateTime.now();

  Collection copyWith({
    String? id,
    String? name,
    String? description,
    String? coverPath,
    DateTime? createdDate,
    DateTime? modifiedDate,
    int? bookCount,
    bool? isDefault,
  }) {
    return Collection(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      coverPath: coverPath ?? this.coverPath,
      createdDate: createdDate ?? this.createdDate,
      modifiedDate: modifiedDate ?? this.modifiedDate,
      bookCount: bookCount ?? this.bookCount,
      isDefault: isDefault ?? this.isDefault,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'coverPath': coverPath,
      'createdDate': createdDate.millisecondsSinceEpoch,
      'modifiedDate': modifiedDate?.millisecondsSinceEpoch,
      'bookCount': bookCount,
      'isDefault': isDefault ? 1 : 0,
    };
  }

  factory Collection.fromMap(Map<String, dynamic> map) {
    return Collection(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      coverPath: map['coverPath'],
      createdDate: DateTime.fromMillisecondsSinceEpoch(map['createdDate']),
      modifiedDate: map['modifiedDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['modifiedDate'])
          : null,
      bookCount: map['bookCount'] ?? 0,
      isDefault: map['isDefault'] == 1,
    );
  }

  @override
  String toString() {
    return 'Collection{id: $id, name: $name, bookCount: $bookCount}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Collection && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

class BookCollection {
  final String id;
  final String bookId;
  final String collectionId;
  final DateTime addedDate;

  BookCollection({
    String? id,
    required this.bookId,
    required this.collectionId,
    DateTime? addedDate,
  }) : id = id ?? const Uuid().v4(),
       addedDate = addedDate ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'bookId': bookId,
      'collectionId': collectionId,
      'addedDate': addedDate.millisecondsSinceEpoch,
    };
  }

  factory BookCollection.fromMap(Map<String, dynamic> map) {
    return BookCollection(
      id: map['id'],
      bookId: map['bookId'],
      collectionId: map['collectionId'],
      addedDate: DateTime.fromMillisecondsSinceEpoch(map['addedDate']),
    );
  }

  @override
  String toString() {
    return 'BookCollection{id: $id, bookId: $bookId, collectionId: $collectionId}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BookCollection && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
