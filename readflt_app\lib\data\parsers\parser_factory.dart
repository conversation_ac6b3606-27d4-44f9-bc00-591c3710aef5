import 'book_parser.dart';
import 'epub_parser.dart';
import 'txt_parser.dart';
import 'pdf_parser.dart';
import '../../core/utils/file_utils.dart';

class ParserFactory {
  static final Map<String, BookParser> _parsers = {
    'epub': EpubParser(),
    'txt': TxtParser(),
    'pdf': PdfParser(),
  };

  /// Get parser for the given file path
  static BookParser? getParser(String filePath) {
    final extension = FileUtils.getFileExtension(filePath);
    return _parsers[extension];
  }

  /// Get parser for the given file extension
  static BookParser? getParserByExtension(String extension) {
    return _parsers[extension.toLowerCase()];
  }

  /// Check if the file format is supported
  static bool isSupported(String filePath) {
    return getParser(filePath) != null;
  }

  /// Get all supported file extensions
  static List<String> getSupportedExtensions() {
    return _parsers.keys.toList();
  }

  /// Get all available parsers
  static List<BookParser> getAllParsers() {
    return _parsers.values.toList();
  }

  /// Register a new parser
  static void registerParser(String extension, BookParser parser) {
    _parsers[extension.toLowerCase()] = parser;
  }

  /// Unregister a parser
  static void unregisterParser(String extension) {
    _parsers.remove(extension.toLowerCase());
  }
}
