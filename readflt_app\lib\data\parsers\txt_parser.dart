import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as path;
import '../models/book.dart';
import 'book_parser.dart';
import '../../core/utils/file_utils.dart';

class TxtParser extends BookParser {
  static const int _wordsPerPage = 300; // Approximate words per page
  static const int _charsPerPage = 2000; // Approximate characters per page

  @override
  List<String> get supportedExtensions => ['txt'];

  @override
  bool supports(String filePath) {
    return supportedExtensions.contains(FileUtils.getFileExtension(filePath));
  }

  @override
  Future<Book> parseMetadata(File file) async {
    try {
      final content = await _readFileContent(file);
      final title = _extractTitle(file, content);
      final author = _extractAuthor(content);
      final description = _extractDescription(content);
      final totalPages = _calculatePages(content);
      
      final fileStats = await file.stat();
      
      return Book(
        title: title,
        author: author,
        description: description,
        filePath: file.path,
        format: 'txt',
        totalPages: totalPages,
        fileSize: fileStats.size,
        tags: _extractTags(content),
      );
    } catch (e) {
      throw BookParserException(
        'Failed to parse TXT metadata',
        filePath: file.path,
        originalException: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  @override
  Future<String> extractContent(File file, {int? chapter}) async {
    try {
      final content = await _readFileContent(file);
      
      if (chapter != null) {
        final pages = _splitIntoPages(content);
        if (chapter < pages.length) {
          return pages[chapter];
        }
        throw BookParserException('Page $chapter not found');
      }
      
      return content;
    } catch (e) {
      throw BookParserException(
        'Failed to extract TXT content',
        filePath: file.path,
        originalException: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  @override
  Future<int> getTotalPages(File file) async {
    try {
      final content = await _readFileContent(file);
      return _calculatePages(content);
    } catch (e) {
      throw BookParserException(
        'Failed to get TXT page count',
        filePath: file.path,
        originalException: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  @override
  Future<File?> extractCover(File file, String outputPath) async {
    // TXT files don't have covers
    return null;
  }

  Future<String> _readFileContent(File file) async {
    try {
      // Try UTF-8 first
      return await file.readAsString(encoding: utf8);
    } catch (e) {
      try {
        // Fallback to Latin-1 if UTF-8 fails
        return await file.readAsString(encoding: latin1);
      } catch (e) {
        // Last resort: read as bytes and convert
        final bytes = await file.readAsBytes();
        return String.fromCharCodes(bytes);
      }
    }
  }

  String _extractTitle(File file, String content) {
    // Try to extract title from first few lines
    final lines = content.split('\n').take(10).toList();
    
    for (final line in lines) {
      final trimmed = line.trim();
      if (trimmed.isNotEmpty && trimmed.length < 100) {
        // Check if it looks like a title (not too long, not all caps unless short)
        if (trimmed.length <= 50 || 
            (trimmed.length <= 100 && !_isAllCaps(trimmed))) {
          return trimmed;
        }
      }
    }
    
    // Fallback to filename
    return path.basenameWithoutExtension(file.path);
  }

  String _extractAuthor(String content) {
    // Look for common author patterns in the first 1000 characters
    final beginning = content.length > 1000 ? content.substring(0, 1000) : content;
    
    final authorPatterns = [
      RegExp(r'(?:作者|Author|By|Written by)[：:\s]+([^\n\r]+)', caseSensitive: false),
      RegExp(r'([^\n\r]+)(?:\s+著|\s+作)', caseSensitive: false),
    ];
    
    for (final pattern in authorPatterns) {
      final match = pattern.firstMatch(beginning);
      if (match != null) {
        final author = match.group(1)?.trim();
        if (author != null && author.isNotEmpty && author.length < 50) {
          return author;
        }
      }
    }
    
    return 'Unknown Author';
  }

  String? _extractDescription(String content) {
    // Try to extract description from the beginning of the content
    final lines = content.split('\n');
    final StringBuffer description = StringBuffer();
    bool foundContent = false;
    int lineCount = 0;
    
    for (final line in lines) {
      final trimmed = line.trim();
      if (trimmed.isEmpty) {
        if (foundContent) lineCount++;
        continue;
      }
      
      if (!foundContent && (trimmed.length > 50 || lineCount > 5)) {
        foundContent = true;
      }
      
      if (foundContent) {
        description.writeln(trimmed);
        if (description.length > 300) break;
      }
      
      lineCount++;
      if (lineCount > 20) break;
    }
    
    final result = description.toString().trim();
    return result.isNotEmpty && result.length > 20 ? result : null;
  }

  int _calculatePages(String content) {
    // Calculate based on character count (more reliable for different languages)
    return (content.length / _charsPerPage).ceil();
  }

  List<String> _splitIntoPages(String content) {
    final pages = <String>[];
    final length = content.length;
    
    for (int i = 0; i < length; i += _charsPerPage) {
      final end = (i + _charsPerPage < length) ? i + _charsPerPage : length;
      pages.add(content.substring(i, end));
    }
    
    return pages;
  }

  List<String> _extractTags(String content) {
    final tags = <String>[];
    
    // Add encoding detection as tag
    if (_containsChinese(content)) {
      tags.add('Chinese');
    }
    if (_containsEnglish(content)) {
      tags.add('English');
    }
    
    // Add estimated reading time
    final wordCount = _estimateWordCount(content);
    final readingMinutes = (wordCount / 200).ceil(); // 200 words per minute
    tags.add('Reading time: ${readingMinutes}min');
    
    return tags;
  }

  bool _isAllCaps(String text) {
    return text == text.toUpperCase() && text != text.toLowerCase();
  }

  bool _containsChinese(String text) {
    return RegExp(r'[\u4e00-\u9fff]').hasMatch(text);
  }

  bool _containsEnglish(String text) {
    return RegExp(r'[a-zA-Z]').hasMatch(text);
  }

  int _estimateWordCount(String content) {
    // Simple word count estimation
    return content.split(RegExp(r'\s+')).length;
  }
}
