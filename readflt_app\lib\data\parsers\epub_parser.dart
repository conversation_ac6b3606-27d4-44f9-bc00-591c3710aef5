import 'dart:io';
import 'package:epubx/epubx.dart';
import 'package:path/path.dart' as path;
import '../models/book.dart';
import 'book_parser.dart';
import '../../core/utils/file_utils.dart';

class EpubParser extends BookParser {
  @override
  List<String> get supportedExtensions => ['epub'];

  @override
  bool supports(String filePath) {
    return supportedExtensions.contains(FileUtils.getFileExtension(filePath));
  }

  @override
  Future<Book> parseMetadata(File file) async {
    try {
      final bytes = await file.readAsBytes();
      final epubBook = await EpubReader.readBook(bytes);
      
      final title = epubBook.Title ?? path.basenameWithoutExtension(file.path);
      final author = epubBook.Author ?? 'Unknown Author';
      final description = epubBook.Description;
      final isbn = _extractIsbn(epubBook);
      final publisher = epubBook.Publisher;
      final publishDate = _parseDate(epubBook.Date);
      final language = epubBook.Language;
      
      final fileStats = await file.stat();
      
      return Book(
        title: title,
        author: author,
        description: description,
        filePath: file.path,
        format: 'epub',
        totalPages: epubBook.Chapters?.length ?? 0,
        fileSize: fileStats.size,
        isbn: isbn,
        publisher: publisher,
        publishDate: publishDate,
        tags: _extractTags(epubBook),
      );
    } catch (e) {
      throw BookParserException(
        'Failed to parse EPUB metadata',
        filePath: file.path,
        originalException: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  @override
  Future<String> extractContent(File file, {int? chapter}) async {
    try {
      final bytes = await file.readAsBytes();
      final epubBook = await EpubReader.readBook(bytes);
      
      if (chapter != null && epubBook.Chapters != null) {
        if (chapter < epubBook.Chapters!.length) {
          final chapterContent = epubBook.Chapters![chapter];
          return chapterContent.HtmlContent ?? '';
        }
        throw BookParserException('Chapter $chapter not found');
      }
      
      // Return all content if no specific chapter requested
      final StringBuffer content = StringBuffer();
      if (epubBook.Chapters != null) {
        for (final chapter in epubBook.Chapters!) {
          content.writeln(chapter.HtmlContent ?? '');
        }
      }
      
      return content.toString();
    } catch (e) {
      throw BookParserException(
        'Failed to extract EPUB content',
        filePath: file.path,
        originalException: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  @override
  Future<int> getTotalPages(File file) async {
    try {
      final bytes = await file.readAsBytes();
      final epubBook = await EpubReader.readBook(bytes);
      return epubBook.Chapters?.length ?? 0;
    } catch (e) {
      throw BookParserException(
        'Failed to get EPUB page count',
        filePath: file.path,
        originalException: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  @override
  Future<File?> extractCover(File file, String outputPath) async {
    try {
      final bytes = await file.readAsBytes();
      final epubBook = await EpubReader.readBook(bytes);
      
      if (epubBook.CoverImage != null) {
        final coverFile = File(outputPath);
        await coverFile.writeAsBytes(epubBook.CoverImage!);
        return coverFile;
      }
      
      return null;
    } catch (e) {
      throw BookParserException(
        'Failed to extract EPUB cover',
        filePath: file.path,
        originalException: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  String? _extractIsbn(EpubBook epubBook) {
    // Try to extract ISBN from metadata
    final identifiers = epubBook.Schema?.Package?.Metadata?.Identifiers;
    if (identifiers != null) {
      for (final identifier in identifiers) {
        if (identifier.Scheme?.toLowerCase() == 'isbn') {
          return identifier.Identifier;
        }
      }
    }
    return null;
  }

  DateTime? _parseDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;
    
    try {
      return DateTime.parse(dateString);
    } catch (e) {
      // Try to parse common date formats
      final patterns = [
        RegExp(r'(\d{4})-(\d{2})-(\d{2})'),
        RegExp(r'(\d{4})/(\d{2})/(\d{2})'),
        RegExp(r'(\d{4})'),
      ];
      
      for (final pattern in patterns) {
        final match = pattern.firstMatch(dateString);
        if (match != null) {
          try {
            final year = int.parse(match.group(1)!);
            final month = match.groupCount >= 2 ? int.parse(match.group(2)!) : 1;
            final day = match.groupCount >= 3 ? int.parse(match.group(3)!) : 1;
            return DateTime(year, month, day);
          } catch (e) {
            continue;
          }
        }
      }
    }
    
    return null;
  }

  List<String> _extractTags(EpubBook epubBook) {
    final tags = <String>[];
    
    // Extract subjects as tags
    final subjects = epubBook.Schema?.Package?.Metadata?.Subjects;
    if (subjects != null) {
      tags.addAll(subjects.map((s) => s.Subject).where((s) => s.isNotEmpty));
    }
    
    // Add language as tag if available
    if (epubBook.Language != null && epubBook.Language!.isNotEmpty) {
      tags.add('Language: ${epubBook.Language}');
    }
    
    return tags;
  }
}
