import 'package:sqflite/sqflite.dart';
import '../models/collection.dart';
import '../models/book.dart';
import 'database_helper.dart';

class CollectionDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  Future<String> insertCollection(Collection collection) async {
    final db = await _databaseHelper.database;
    await db.insert('collections', collection.toMap());
    return collection.id;
  }

  Future<Collection?> getCollectionById(String id) async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'collections',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Collection.fromMap(maps.first);
    }
    return null;
  }

  Future<List<Collection>> getAllCollections() async {
    final db = await _databaseHelper.database;
    final maps = await db.query('collections', orderBy: 'createdDate DESC');
    return maps.map((map) => Collection.fromMap(map)).toList();
  }

  Future<List<Collection>> getDefaultCollections() async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'collections',
      where: 'isDefault = ?',
      whereArgs: [1],
      orderBy: 'createdDate ASC',
    );
    return maps.map((map) => Collection.fromMap(map)).toList();
  }

  Future<List<Collection>> getUserCollections() async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'collections',
      where: 'isDefault = ?',
      whereArgs: [0],
      orderBy: 'createdDate DESC',
    );
    return maps.map((map) => Collection.fromMap(map)).toList();
  }

  Future<int> updateCollection(Collection collection) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'collections',
      collection.copyWith(modifiedDate: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [collection.id],
    );
  }

  Future<int> deleteCollection(String id) async {
    final db = await _databaseHelper.database;
    
    // First delete all book-collection relationships
    await db.delete(
      'book_collections',
      where: 'collectionId = ?',
      whereArgs: [id],
    );
    
    // Then delete the collection
    return await db.delete(
      'collections',
      where: 'id = ? AND isDefault = ?',
      whereArgs: [id, 0], // Only allow deletion of non-default collections
    );
  }

  Future<String> addBookToCollection(String bookId, String collectionId) async {
    final db = await _databaseHelper.database;
    final bookCollection = BookCollection(
      bookId: bookId,
      collectionId: collectionId,
    );
    
    await db.insert(
      'book_collections',
      bookCollection.toMap(),
      conflictAlgorithm: ConflictAlgorithm.ignore,
    );
    
    // Update collection book count
    await _updateCollectionBookCount(collectionId);
    
    return bookCollection.id;
  }

  Future<int> removeBookFromCollection(String bookId, String collectionId) async {
    final db = await _databaseHelper.database;
    final result = await db.delete(
      'book_collections',
      where: 'bookId = ? AND collectionId = ?',
      whereArgs: [bookId, collectionId],
    );
    
    // Update collection book count
    await _updateCollectionBookCount(collectionId);
    
    return result;
  }

  Future<List<Book>> getBooksInCollection(String collectionId) async {
    final db = await _databaseHelper.database;
    final maps = await db.rawQuery('''
      SELECT b.* FROM books b
      INNER JOIN book_collections bc ON b.id = bc.bookId
      WHERE bc.collectionId = ?
      ORDER BY bc.addedDate DESC
    ''', [collectionId]);
    
    return maps.map((map) => Book.fromMap(map)).toList();
  }

  Future<List<Collection>> getCollectionsForBook(String bookId) async {
    final db = await _databaseHelper.database;
    final maps = await db.rawQuery('''
      SELECT c.* FROM collections c
      INNER JOIN book_collections bc ON c.id = bc.collectionId
      WHERE bc.bookId = ?
      ORDER BY c.name ASC
    ''', [bookId]);
    
    return maps.map((map) => Collection.fromMap(map)).toList();
  }

  Future<bool> isBookInCollection(String bookId, String collectionId) async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'book_collections',
      where: 'bookId = ? AND collectionId = ?',
      whereArgs: [bookId, collectionId],
    );
    
    return maps.isNotEmpty;
  }

  Future<void> _updateCollectionBookCount(String collectionId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT COUNT(*) as count FROM book_collections
      WHERE collectionId = ?
    ''', [collectionId]);
    
    final count = Sqflite.firstIntValue(result) ?? 0;
    
    await db.update(
      'collections',
      {'bookCount': count, 'modifiedDate': DateTime.now().millisecondsSinceEpoch},
      where: 'id = ?',
      whereArgs: [collectionId],
    );
  }

  Future<int> getCollectionCount() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM collections');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  Future<Collection?> getFavoriteCollection() async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'collections',
      where: 'name = ? AND isDefault = ?',
      whereArgs: ['我的收藏', 1],
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return Collection.fromMap(maps.first);
    }
    return null;
  }

  Future<Collection?> getRecentCollection() async {
    final db = await _databaseHelper.database;
    final maps = await db.query(
      'collections',
      where: 'name = ? AND isDefault = ?',
      whereArgs: ['最近阅读', 1],
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return Collection.fromMap(maps.first);
    }
    return null;
  }
}
