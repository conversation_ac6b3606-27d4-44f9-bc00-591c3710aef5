import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import '../models/book.dart';
import '../parsers/parser_factory.dart';
import '../parsers/book_parser.dart';
import '../database/book_dao.dart';
import '../../core/utils/file_utils.dart';

class FileImportService {
  final BookDao _bookDao = BookDao();
  final Uuid _uuid = const Uuid();

  /// Import a single file
  Future<Book> importFile(File file) async {
    if (!await file.exists()) {
      throw FileImportException('File does not exist: ${file.path}');
    }

    if (!FileUtils.isSupportedFormat(file.path)) {
      throw FileImportException('Unsupported file format: ${FileUtils.getFileExtension(file.path)}');
    }

    final parser = ParserFactory.getParser(file.path);
    if (parser == null) {
      throw FileImportException('No parser available for file: ${file.path}');
    }

    try {
      // Copy file to books directory
      final copiedFilePath = await FileUtils.copyFileToBooks(file);
      final copiedFile = File(copiedFilePath);

      // Parse metadata
      final book = await parser.parseMetadata(copiedFile);
      
      // Extract and save cover if available
      String? coverPath;
      try {
        final coversDir = await FileUtils.getCoversDirectory();
        final coverFileName = '${book.id}.jpg';
        final coverFilePath = path.join(coversDir.path, coverFileName);
        
        final coverFile = await parser.extractCover(copiedFile, coverFilePath);
        if (coverFile != null && await coverFile.exists()) {
          coverPath = coverFile.path;
        }
      } catch (e) {
        // Cover extraction failed, but continue with import
        print('Failed to extract cover: $e');
      }

      // Update book with cover path and correct file path
      final finalBook = book.copyWith(
        filePath: copiedFilePath,
        coverPath: coverPath,
      );

      // Save to database
      await _bookDao.insertBook(finalBook);

      return finalBook;
    } catch (e) {
      throw FileImportException(
        'Failed to import file: ${file.path}',
        originalException: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  /// Import multiple files
  Future<List<ImportResult>> importFiles(List<File> files) async {
    final results = <ImportResult>[];

    for (final file in files) {
      try {
        final book = await importFile(file);
        results.add(ImportResult.success(book));
      } catch (e) {
        results.add(ImportResult.failure(file.path, e.toString()));
      }
    }

    return results;
  }

  /// Import files from directory
  Future<List<ImportResult>> importFromDirectory(Directory directory, {bool recursive = false}) async {
    if (!await directory.exists()) {
      throw FileImportException('Directory does not exist: ${directory.path}');
    }

    final files = <File>[];
    final entities = await directory.list(recursive: recursive).toList();

    for (final entity in entities) {
      if (entity is File && FileUtils.isSupportedFormat(entity.path)) {
        files.add(entity);
      }
    }

    return await importFiles(files);
  }

  /// Check if file is already imported
  Future<bool> isFileImported(File file) async {
    final fileName = path.basename(file.path);
    final books = await _bookDao.getAllBooks();
    
    return books.any((book) => path.basename(book.filePath) == fileName);
  }

  /// Get import progress callback
  Stream<ImportProgress> importFilesWithProgress(List<File> files) async* {
    int completed = 0;
    final total = files.length;
    final results = <ImportResult>[];

    yield ImportProgress(completed, total, results);

    for (final file in files) {
      try {
        final book = await importFile(file);
        results.add(ImportResult.success(book));
      } catch (e) {
        results.add(ImportResult.failure(file.path, e.toString()));
      }

      completed++;
      yield ImportProgress(completed, total, List.from(results));
    }
  }

  /// Validate file before import
  Future<ValidationResult> validateFile(File file) async {
    if (!await file.exists()) {
      return ValidationResult.invalid('File does not exist');
    }

    if (!FileUtils.isSupportedFormat(file.path)) {
      return ValidationResult.invalid('Unsupported file format: ${FileUtils.getFileExtension(file.path)}');
    }

    final parser = ParserFactory.getParser(file.path);
    if (parser == null) {
      return ValidationResult.invalid('No parser available for this file type');
    }

    try {
      // Try to parse metadata to validate file integrity
      await parser.parseMetadata(file);
      return ValidationResult.valid();
    } catch (e) {
      return ValidationResult.invalid('File appears to be corrupted: ${e.toString()}');
    }
  }
}

class FileImportException implements Exception {
  final String message;
  final Exception? originalException;

  FileImportException(this.message, {this.originalException});

  @override
  String toString() {
    var result = 'FileImportException: $message';
    if (originalException != null) {
      result += '\nCaused by: $originalException';
    }
    return result;
  }
}

class ImportResult {
  final bool isSuccess;
  final Book? book;
  final String? filePath;
  final String? error;

  ImportResult.success(this.book) 
      : isSuccess = true, filePath = book?.filePath, error = null;
  
  ImportResult.failure(this.filePath, this.error) 
      : isSuccess = false, book = null;
}

class ImportProgress {
  final int completed;
  final int total;
  final List<ImportResult> results;

  ImportProgress(this.completed, this.total, this.results);

  double get progress => total > 0 ? completed / total : 0.0;
  bool get isComplete => completed >= total;
}

class ValidationResult {
  final bool isValid;
  final String? error;

  ValidationResult.valid() : isValid = true, error = null;
  ValidationResult.invalid(this.error) : isValid = false;
}
