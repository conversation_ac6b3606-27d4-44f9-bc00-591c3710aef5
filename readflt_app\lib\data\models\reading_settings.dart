class ReadingSettings {
  final double fontSize;
  final String fontFamily;
  final double lineHeight;
  final double letterSpacing;
  final int backgroundColor;
  final int textColor;
  final bool isDarkMode;
  final double brightness;
  final int pageTransition;
  final bool keepScreenOn;
  final bool showPageNumbers;
  final bool showProgress;
  final double marginHorizontal;
  final double marginVertical;

  const ReadingSettings({
    this.fontSize = 16.0,
    this.fontFamily = 'System',
    this.lineHeight = 1.5,
    this.letterSpacing = 0.0,
    this.backgroundColor = 0xFFFFFFFF,
    this.textColor = 0xFF000000,
    this.isDarkMode = false,
    this.brightness = 1.0,
    this.pageTransition = 0, // 0: slide, 1: fade, 2: curl
    this.keepScreenOn = false,
    this.showPageNumbers = true,
    this.showProgress = true,
    this.marginHorizontal = 16.0,
    this.marginVertical = 24.0,
  });

  ReadingSettings copyWith({
    double? fontSize,
    String? fontFamily,
    double? lineHeight,
    double? letterSpacing,
    int? backgroundColor,
    int? textColor,
    bool? isDarkMode,
    double? brightness,
    int? pageTransition,
    bool? keepScreenOn,
    bool? showPageNumbers,
    bool? showProgress,
    double? marginHorizontal,
    double? marginVertical,
  }) {
    return ReadingSettings(
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
      lineHeight: lineHeight ?? this.lineHeight,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      textColor: textColor ?? this.textColor,
      isDarkMode: isDarkMode ?? this.isDarkMode,
      brightness: brightness ?? this.brightness,
      pageTransition: pageTransition ?? this.pageTransition,
      keepScreenOn: keepScreenOn ?? this.keepScreenOn,
      showPageNumbers: showPageNumbers ?? this.showPageNumbers,
      showProgress: showProgress ?? this.showProgress,
      marginHorizontal: marginHorizontal ?? this.marginHorizontal,
      marginVertical: marginVertical ?? this.marginVertical,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'fontSize': fontSize,
      'fontFamily': fontFamily,
      'lineHeight': lineHeight,
      'letterSpacing': letterSpacing,
      'backgroundColor': backgroundColor,
      'textColor': textColor,
      'isDarkMode': isDarkMode ? 1 : 0,
      'brightness': brightness,
      'pageTransition': pageTransition,
      'keepScreenOn': keepScreenOn ? 1 : 0,
      'showPageNumbers': showPageNumbers ? 1 : 0,
      'showProgress': showProgress ? 1 : 0,
      'marginHorizontal': marginHorizontal,
      'marginVertical': marginVertical,
    };
  }

  factory ReadingSettings.fromMap(Map<String, dynamic> map) {
    return ReadingSettings(
      fontSize: map['fontSize'] ?? 16.0,
      fontFamily: map['fontFamily'] ?? 'System',
      lineHeight: map['lineHeight'] ?? 1.5,
      letterSpacing: map['letterSpacing'] ?? 0.0,
      backgroundColor: map['backgroundColor'] ?? 0xFFFFFFFF,
      textColor: map['textColor'] ?? 0xFF000000,
      isDarkMode: map['isDarkMode'] == 1,
      brightness: map['brightness'] ?? 1.0,
      pageTransition: map['pageTransition'] ?? 0,
      keepScreenOn: map['keepScreenOn'] == 1,
      showPageNumbers: map['showPageNumbers'] == 1,
      showProgress: map['showProgress'] == 1,
      marginHorizontal: map['marginHorizontal'] ?? 16.0,
      marginVertical: map['marginVertical'] ?? 24.0,
    );
  }

  @override
  String toString() {
    return 'ReadingSettings{fontSize: $fontSize, fontFamily: $fontFamily, isDarkMode: $isDarkMode}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ReadingSettings &&
          runtimeType == other.runtimeType &&
          fontSize == other.fontSize &&
          fontFamily == other.fontFamily &&
          lineHeight == other.lineHeight &&
          letterSpacing == other.letterSpacing &&
          backgroundColor == other.backgroundColor &&
          textColor == other.textColor &&
          isDarkMode == other.isDarkMode &&
          brightness == other.brightness &&
          pageTransition == other.pageTransition &&
          keepScreenOn == other.keepScreenOn &&
          showPageNumbers == other.showPageNumbers &&
          showProgress == other.showProgress &&
          marginHorizontal == other.marginHorizontal &&
          marginVertical == other.marginVertical;

  @override
  int get hashCode {
    return Object.hash(
      fontSize,
      fontFamily,
      lineHeight,
      letterSpacing,
      backgroundColor,
      textColor,
      isDarkMode,
      brightness,
      pageTransition,
      keepScreenOn,
      showPageNumbers,
      showProgress,
      marginHorizontal,
      marginVertical,
    );
  }
}
