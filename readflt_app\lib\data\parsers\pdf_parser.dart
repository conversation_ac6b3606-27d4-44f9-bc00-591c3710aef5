import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:syncfusion_flutter_pdf/pdf.dart';
import '../models/book.dart';
import 'book_parser.dart';
import '../../core/utils/file_utils.dart';

class PdfParser extends BookParser {
  @override
  List<String> get supportedExtensions => ['pdf'];

  @override
  bool supports(String filePath) {
    return supportedExtensions.contains(FileUtils.getFileExtension(filePath));
  }

  @override
  Future<Book> parseMetadata(File file) async {
    try {
      final bytes = await file.readAsBytes();
      final document = PdfDocument(inputBytes: bytes);
      
      final title = _extractTitle(document, file);
      final author = _extractAuthor(document);
      final description = _extractDescription(document);
      final totalPages = document.pages.count;
      
      final fileStats = await file.stat();
      
      document.dispose();
      
      return Book(
        title: title,
        author: author,
        description: description,
        filePath: file.path,
        format: 'pdf',
        totalPages: totalPages,
        fileSize: fileStats.size,
        tags: _extractTags(document),
      );
    } catch (e) {
      throw BookParserException(
        'Failed to parse PDF metadata',
        filePath: file.path,
        originalException: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  @override
  Future<String> extractContent(File file, {int? chapter}) async {
    try {
      final bytes = await file.readAsBytes();
      final document = PdfDocument(inputBytes: bytes);
      
      if (chapter != null) {
        if (chapter < document.pages.count) {
          final page = document.pages[chapter];
          final text = PdfTextExtractor(document).extractText(startPageIndex: chapter, endPageIndex: chapter);
          document.dispose();
          return text;
        }
        document.dispose();
        throw BookParserException('Page $chapter not found');
      }
      
      // Extract all text if no specific page requested
      final text = PdfTextExtractor(document).extractText();
      document.dispose();
      return text;
    } catch (e) {
      throw BookParserException(
        'Failed to extract PDF content',
        filePath: file.path,
        originalException: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  @override
  Future<int> getTotalPages(File file) async {
    try {
      final bytes = await file.readAsBytes();
      final document = PdfDocument(inputBytes: bytes);
      final pageCount = document.pages.count;
      document.dispose();
      return pageCount;
    } catch (e) {
      throw BookParserException(
        'Failed to get PDF page count',
        filePath: file.path,
        originalException: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  @override
  Future<File?> extractCover(File file, String outputPath) async {
    try {
      final bytes = await file.readAsBytes();
      final document = PdfDocument(inputBytes: bytes);
      
      if (document.pages.count > 0) {
        final page = document.pages[0];
        final image = page.graphics.toImage();
        if (image != null) {
          final imageBytes = image.save();
          final coverFile = File(outputPath);
          await coverFile.writeAsBytes(imageBytes);
          document.dispose();
          return coverFile;
        }
      }
      
      document.dispose();
      return null;
    } catch (e) {
      throw BookParserException(
        'Failed to extract PDF cover',
        filePath: file.path,
        originalException: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  String _extractTitle(PdfDocument document, File file) {
    try {
      final title = document.documentInformation.title;
      if (title != null && title.trim().isNotEmpty) {
        return title.trim();
      }
    } catch (e) {
      // Ignore errors and fallback to filename
    }
    
    return path.basenameWithoutExtension(file.path);
  }

  String _extractAuthor(PdfDocument document) {
    try {
      final author = document.documentInformation.author;
      if (author != null && author.trim().isNotEmpty) {
        return author.trim();
      }
    } catch (e) {
      // Ignore errors
    }
    
    return 'Unknown Author';
  }

  String? _extractDescription(PdfDocument document) {
    try {
      final subject = document.documentInformation.subject;
      if (subject != null && subject.trim().isNotEmpty) {
        return subject.trim();
      }
      
      // Try to extract text from first page as description
      if (document.pages.count > 0) {
        final firstPageText = PdfTextExtractor(document).extractText(startPageIndex: 0, endPageIndex: 0);
        if (firstPageText.isNotEmpty && firstPageText.length > 50) {
          // Take first 300 characters as description
          final description = firstPageText.length > 300 
              ? '${firstPageText.substring(0, 300)}...'
              : firstPageText;
          return description.trim();
        }
      }
    } catch (e) {
      // Ignore errors
    }
    
    return null;
  }

  List<String> _extractTags(PdfDocument document) {
    final tags = <String>[];
    
    try {
      // Add creation date as tag
      final creationDate = document.documentInformation.creationDate;
      if (creationDate != null) {
        tags.add('Created: ${creationDate.year}');
      }
      
      // Add page count as tag
      tags.add('Pages: ${document.pages.count}');
      
      // Add keywords if available
      final keywords = document.documentInformation.keywords;
      if (keywords != null && keywords.trim().isNotEmpty) {
        final keywordList = keywords.split(',').map((k) => k.trim()).where((k) => k.isNotEmpty);
        tags.addAll(keywordList);
      }
      
      // Add producer/creator info
      final producer = document.documentInformation.producer;
      if (producer != null && producer.trim().isNotEmpty) {
        tags.add('Producer: $producer');
      }
    } catch (e) {
      // Ignore errors in tag extraction
    }
    
    return tags;
  }
}
