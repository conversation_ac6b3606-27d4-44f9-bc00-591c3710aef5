import 'dart:io';
import '../models/book.dart';

abstract class BookParser {
  /// Parse book metadata from file
  Future<Book> parseMetadata(File file);
  
  /// Extract book content for reading
  Future<String> extractContent(File file, {int? chapter});
  
  /// Get total number of pages/chapters
  Future<int> getTotalPages(File file);
  
  /// Extract cover image if available
  Future<File?> extractCover(File file, String outputPath);
  
  /// Check if this parser supports the given file
  bool supports(String filePath);
  
  /// Get supported file extensions
  List<String> get supportedExtensions;
}

class BookParserException implements Exception {
  final String message;
  final String? filePath;
  final Exception? originalException;

  BookParserException(
    this.message, {
    this.filePath,
    this.originalException,
  });

  @override
  String toString() {
    var result = 'BookParserException: $message';
    if (filePath != null) {
      result += ' (File: $filePath)';
    }
    if (originalException != null) {
      result += '\nCaused by: $originalException';
    }
    return result;
  }
}

class ParsedBookContent {
  final String content;
  final String? title;
  final int? chapterIndex;
  final String? chapterTitle;

  ParsedBookContent({
    required this.content,
    this.title,
    this.chapterIndex,
    this.chapterTitle,
  });
}

class BookMetadata {
  final String title;
  final String author;
  final String? description;
  final String? isbn;
  final String? publisher;
  final DateTime? publishDate;
  final List<String> tags;
  final String? language;
  final File? coverImage;

  BookMetadata({
    required this.title,
    required this.author,
    this.description,
    this.isbn,
    this.publisher,
    this.publishDate,
    this.tags = const [],
    this.language,
    this.coverImage,
  });
}
