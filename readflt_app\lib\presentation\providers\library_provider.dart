import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/book.dart';
import '../../data/database/book_dao.dart';
import '../../data/services/file_import_service.dart';

enum SortOption {
  titleAsc,
  titleDesc,
  authorAsc,
  authorDesc,
  dateAddedAsc,
  dateAddedDesc,
  lastReadAsc,
  lastReadDesc,
  progressAsc,
  progressDesc,
}

enum ViewMode {
  grid,
  list,
}

class LibraryState {
  final List<Book> books;
  final List<Book> filteredBooks;
  final bool isLoading;
  final String? error;
  final String searchQuery;
  final SortOption sortOption;
  final ViewMode viewMode;
  final String? selectedFormat;
  final List<String> selectedTags;
  final bool showFavoritesOnly;

  const LibraryState({
    this.books = const [],
    this.filteredBooks = const [],
    this.isLoading = false,
    this.error,
    this.searchQuery = '',
    this.sortOption = SortOption.dateAddedDesc,
    this.viewMode = ViewMode.grid,
    this.selectedFormat,
    this.selectedTags = const [],
    this.showFavoritesOnly = false,
  });

  LibraryState copyWith({
    List<Book>? books,
    List<Book>? filteredBooks,
    bool? isLoading,
    String? error,
    String? searchQuery,
    SortOption? sortOption,
    ViewMode? viewMode,
    String? selectedFormat,
    List<String>? selectedTags,
    bool? showFavoritesOnly,
  }) {
    return LibraryState(
      books: books ?? this.books,
      filteredBooks: filteredBooks ?? this.filteredBooks,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      searchQuery: searchQuery ?? this.searchQuery,
      sortOption: sortOption ?? this.sortOption,
      viewMode: viewMode ?? this.viewMode,
      selectedFormat: selectedFormat,
      selectedTags: selectedTags ?? this.selectedTags,
      showFavoritesOnly: showFavoritesOnly ?? this.showFavoritesOnly,
    );
  }
}

class LibraryNotifier extends StateNotifier<LibraryState> {
  final BookDao _bookDao;
  final FileImportService _importService;

  LibraryNotifier(this._bookDao, this._importService) : super(const LibraryState()) {
    loadBooks();
  }

  Future<void> loadBooks() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final books = await _bookDao.getAllBooks();
      state = state.copyWith(
        books: books,
        isLoading: false,
      );
      _applyFiltersAndSort();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  void setSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
    _applyFiltersAndSort();
  }

  void setSortOption(SortOption option) {
    state = state.copyWith(sortOption: option);
    _applyFiltersAndSort();
  }

  void setViewMode(ViewMode mode) {
    state = state.copyWith(viewMode: mode);
  }

  void setFormatFilter(String? format) {
    state = state.copyWith(selectedFormat: format);
    _applyFiltersAndSort();
  }

  void toggleTagFilter(String tag) {
    final currentTags = List<String>.from(state.selectedTags);
    if (currentTags.contains(tag)) {
      currentTags.remove(tag);
    } else {
      currentTags.add(tag);
    }
    state = state.copyWith(selectedTags: currentTags);
    _applyFiltersAndSort();
  }

  void clearTagFilters() {
    state = state.copyWith(selectedTags: []);
    _applyFiltersAndSort();
  }

  void toggleFavoritesOnly() {
    state = state.copyWith(showFavoritesOnly: !state.showFavoritesOnly);
    _applyFiltersAndSort();
  }

  void clearAllFilters() {
    state = state.copyWith(
      searchQuery: '',
      selectedFormat: null,
      selectedTags: [],
      showFavoritesOnly: false,
    );
    _applyFiltersAndSort();
  }

  Future<void> toggleBookFavorite(String bookId) async {
    try {
      await _bookDao.toggleBookFavorite(bookId);
      await loadBooks(); // Reload to get updated data
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> deleteBook(String bookId) async {
    try {
      await _bookDao.deleteBook(bookId);
      await loadBooks(); // Reload to get updated data
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  void _applyFiltersAndSort() {
    var filtered = List<Book>.from(state.books);

    // Apply search filter
    if (state.searchQuery.isNotEmpty) {
      final query = state.searchQuery.toLowerCase();
      filtered = filtered.where((book) =>
          book.title.toLowerCase().contains(query) ||
          book.author.toLowerCase().contains(query) ||
          (book.description?.toLowerCase().contains(query) ?? false)).toList();
    }

    // Apply format filter
    if (state.selectedFormat != null) {
      filtered = filtered.where((book) => book.format == state.selectedFormat).toList();
    }

    // Apply tag filters
    if (state.selectedTags.isNotEmpty) {
      filtered = filtered.where((book) {
        return state.selectedTags.any((tag) => book.tags.contains(tag));
      }).toList();
    }

    // Apply favorites filter
    if (state.showFavoritesOnly) {
      filtered = filtered.where((book) => book.isFavorite).toList();
    }

    // Apply sorting
    switch (state.sortOption) {
      case SortOption.titleAsc:
        filtered.sort((a, b) => a.title.compareTo(b.title));
        break;
      case SortOption.titleDesc:
        filtered.sort((a, b) => b.title.compareTo(a.title));
        break;
      case SortOption.authorAsc:
        filtered.sort((a, b) => a.author.compareTo(b.author));
        break;
      case SortOption.authorDesc:
        filtered.sort((a, b) => b.author.compareTo(a.author));
        break;
      case SortOption.dateAddedAsc:
        filtered.sort((a, b) => a.addedDate.compareTo(b.addedDate));
        break;
      case SortOption.dateAddedDesc:
        filtered.sort((a, b) => b.addedDate.compareTo(a.addedDate));
        break;
      case SortOption.lastReadAsc:
        filtered.sort((a, b) {
          if (a.lastReadDate == null && b.lastReadDate == null) return 0;
          if (a.lastReadDate == null) return 1;
          if (b.lastReadDate == null) return -1;
          return a.lastReadDate!.compareTo(b.lastReadDate!);
        });
        break;
      case SortOption.lastReadDesc:
        filtered.sort((a, b) {
          if (a.lastReadDate == null && b.lastReadDate == null) return 0;
          if (a.lastReadDate == null) return 1;
          if (b.lastReadDate == null) return -1;
          return b.lastReadDate!.compareTo(a.lastReadDate!);
        });
        break;
      case SortOption.progressAsc:
        filtered.sort((a, b) => a.progress.compareTo(b.progress));
        break;
      case SortOption.progressDesc:
        filtered.sort((a, b) => b.progress.compareTo(a.progress));
        break;
    }

    state = state.copyWith(filteredBooks: filtered);
  }
}

// Providers
final bookDaoProvider = Provider<BookDao>((ref) => BookDao());
final fileImportServiceProvider = Provider<FileImportService>((ref) => FileImportService());

final libraryProvider = StateNotifierProvider<LibraryNotifier, LibraryState>((ref) {
  final bookDao = ref.watch(bookDaoProvider);
  final importService = ref.watch(fileImportServiceProvider);
  return LibraryNotifier(bookDao, importService);
});

// Helper providers
final availableFormatsProvider = FutureProvider<List<String>>((ref) async {
  final bookDao = ref.watch(bookDaoProvider);
  final books = await bookDao.getAllBooks();
  return books.map((book) => book.format).toSet().toList()..sort();
});

final availableTagsProvider = FutureProvider<List<String>>((ref) async {
  final bookDao = ref.watch(bookDaoProvider);
  return await bookDao.getAllTags();
});

final bookStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final bookDao = ref.watch(bookDaoProvider);
  final totalBooks = await bookDao.getBookCount();
  final formatCounts = await bookDao.getBookCountByFormat();
  final favoriteBooks = await bookDao.getFavoriteBooks();
  
  return {
    'totalBooks': totalBooks,
    'formatCounts': formatCounts,
    'favoriteCount': favoriteBooks.length,
  };
});
