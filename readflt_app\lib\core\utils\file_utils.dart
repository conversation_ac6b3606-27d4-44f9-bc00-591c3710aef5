import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import '../constants/app_constants.dart';

class FileUtils {
  static Future<Directory> getAppDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final readfltDir = Directory(path.join(appDir.path, AppConstants.appName));
    if (!await readfltDir.exists()) {
      await readfltDir.create(recursive: true);
    }
    return readfltDir;
  }
  
  static Future<Directory> getBooksDirectory() async {
    final appDir = await getAppDirectory();
    final booksDir = Directory(path.join(appDir.path, AppConstants.booksDirectory));
    if (!await booksDir.exists()) {
      await booksDir.create(recursive: true);
    }
    return booksDir;
  }
  
  static Future<Directory> getCoversDirectory() async {
    final appDir = await getAppDirectory();
    final coversDir = Directory(path.join(appDir.path, AppConstants.coversDirectory));
    if (!await coversDir.exists()) {
      await coversDir.create(recursive: true);
    }
    return coversDir;
  }
  
  static Future<Directory> getTempDirectory() async {
    final appDir = await getAppDirectory();
    final tempDir = Directory(path.join(appDir.path, AppConstants.tempDirectory));
    if (!await tempDir.exists()) {
      await tempDir.create(recursive: true);
    }
    return tempDir;
  }
  
  static String getFileExtension(String filePath) {
    return path.extension(filePath).toLowerCase().replaceFirst('.', '');
  }
  
  static bool isSupportedFormat(String filePath) {
    final extension = getFileExtension(filePath);
    return AppConstants.supportedFormats.contains(extension);
  }
  
  static String generateUniqueFileName(String originalName) {
    final extension = path.extension(originalName);
    final nameWithoutExtension = path.basenameWithoutExtension(originalName);
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${nameWithoutExtension}_$timestamp$extension';
  }
  
  static Future<String> copyFileToBooks(File sourceFile) async {
    final booksDir = await getBooksDirectory();
    final fileName = generateUniqueFileName(path.basename(sourceFile.path));
    final targetPath = path.join(booksDir.path, fileName);
    await sourceFile.copy(targetPath);
    return targetPath;
  }
}
